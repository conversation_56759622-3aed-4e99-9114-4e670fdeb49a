package grpcstarter.ptp;

import com.squareup.javapoet.ClassName;
import com.squareup.javapoet.CodeBlock;
import com.squareup.javapoet.FieldSpec;
import com.squareup.javapoet.JavaFile;
import com.squareup.javapoet.MethodSpec;
import com.squareup.javapoet.ParameterizedTypeName;
import com.squareup.javapoet.TypeSpec;
import com.squareup.javapoet.WildcardTypeName;
import java.io.IOException;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.ConcurrentHashMap;
import javax.annotation.processing.AbstractProcessor;
import javax.annotation.processing.Filer;
import javax.annotation.processing.Messager;
import javax.annotation.processing.ProcessingEnvironment;
import javax.annotation.processing.RoundEnvironment;
import javax.annotation.processing.SupportedAnnotationTypes;
import javax.annotation.processing.SupportedSourceVersion;
import javax.lang.model.SourceVersion;
import javax.lang.model.element.Element;
import javax.lang.model.element.ElementKind;
import javax.lang.model.element.Modifier;
import javax.lang.model.element.TypeElement;
import javax.lang.model.type.TypeMirror;
import javax.lang.model.util.Elements;
import javax.lang.model.util.Types;
import javax.tools.Diagnostic;

/**
 * Annotation processor that automatically generates GlobalTypes class for protobuf Message registration.
 * No annotations required - it will automatically scan for protobuf Messages and generate the class.
 *
 * <AUTHOR>
 */
@SupportedAnnotationTypes("*")
@SupportedSourceVersion(SourceVersion.RELEASE_17)
public class ProtobufTypeRegistryProcessor extends AbstractProcessor {

    private Types typeUtils;
    private Elements elementUtils;
    private Filer filer;
    private Messager messager;

    @Override
    public synchronized void init(ProcessingEnvironment processingEnv) {
        super.init(processingEnv);
        typeUtils = processingEnv.getTypeUtils();
        elementUtils = processingEnv.getElementUtils();
        filer = processingEnv.getFiler();
        messager = processingEnv.getMessager();
    }

    @Override
    public boolean process(Set<? extends TypeElement> annotations, RoundEnvironment roundEnv) {
        if (roundEnv.processingOver()) {
            return false;
        }

        System.out.println("roundEnv.getRootElements() = " + roundEnv.getRootElements());

        // Only generate once per compilation when we have root elements
        if (!roundEnv.getRootElements().isEmpty()) {
            try {
                generateGlobalTypes(roundEnv);
            } catch (IOException e) {
                messager.printMessage(Diagnostic.Kind.ERROR, "Failed to generate GlobalTypes: " + e.getMessage());
            }
        }

        return true;
    }

    private void generateGlobalTypes(RoundEnvironment roundEnv) throws IOException {
        // Find all protobuf Message classes
        Set<MessageInfo> messageClasses = findProtobufMessageClasses(roundEnv);

        if (messageClasses.isEmpty()) {
            return;
        }

        // Calculate dynamic package name based on common prefix of all message classes
        String packageName = calculateCommonPackagePrefix(messageClasses);
        String className = "GlobalTypes";

        // Generate the GlobalTypes class
        TypeSpec globalTypesClass = generateGlobalTypesClass(className, messageClasses);

        JavaFile javaFile = JavaFile.builder(packageName, globalTypesClass)
                .addFileComment("Generated by protobuf-typeregistry-processor")
                .build();

        javaFile.writeTo(filer);

        System.out.printf("Generated %s.%s with %d protobuf Message registrations\n", packageName, className, messageClasses.size());
    }

    private String calculateCommonPackagePrefix(Set<MessageInfo> messageClasses) {
        if (messageClasses.isEmpty()) {
            return "grpcstarter.generated";
        }

        // Extract all package names from class names
        List<String> packageNames = messageClasses.stream()
                .map(MessageInfo::getClassName)
                .map(this::extractPackageName)
                .filter(pkg -> !pkg.isEmpty())
                .collect(java.util.stream.Collectors.toList());

        if (packageNames.isEmpty()) {
            return "grpcstarter.generated";
        }

        // Find the longest common prefix
        String commonPrefix = findLongestCommonPrefix(packageNames);

        // If no common prefix or too short, use default
        if (commonPrefix.isEmpty() || commonPrefix.split("\\.").length < 2) {
            return "grpcstarter.generated";
        }

        return commonPrefix;
    }

    private String extractPackageName(String className) {
        int lastDotIndex = className.lastIndexOf('.');
        return lastDotIndex > 0 ? className.substring(0, lastDotIndex) : "";
    }

    private String findLongestCommonPrefix(List<String> packageNames) {
        if (packageNames.isEmpty()) {
            return "";
        }

        String first = packageNames.get(0);
        String[] firstParts = first.split("\\.");

        for (int i = 0; i < firstParts.length; i++) {
            String currentPrefix = String.join(".", java.util.Arrays.copyOf(firstParts, i + 1));

            // Check if all package names start with this prefix
            boolean allMatch = packageNames.stream()
                    .allMatch(pkg -> pkg.startsWith(currentPrefix + ".") || pkg.equals(currentPrefix));

            if (!allMatch) {
                // Return the previous valid prefix
                return i > 0 ? String.join(".", java.util.Arrays.copyOf(firstParts, i)) : "";
            }
        }

        return first;
    }

    private Set<MessageInfo> findProtobufMessageClasses(RoundEnvironment roundEnv) {
        Set<MessageInfo> messageClasses = new TreeSet<>(Comparator.comparing(MessageInfo::getTypeName));

        // Get the protobuf Message type
        TypeElement messageTypeElement = elementUtils.getTypeElement("com.google.protobuf.Message");
        if (messageTypeElement == null) {
            return messageClasses;
        }

        TypeMirror messageType = messageTypeElement.asType();

        // Scan all root elements
        for (Element rootElement : roundEnv.getRootElements()) {
            if (rootElement.getKind() == ElementKind.CLASS) {
                TypeElement typeElement = (TypeElement) rootElement;

                // Check if this class extends Message
                if (isProtobufMessage(typeElement, messageType)) {
                    String className = typeElement.getQualifiedName().toString();
                    String typeName = deriveProtobufTypeName(className);
                    messageClasses.add(new MessageInfo(typeName, className));
                }
            }
        }

        return messageClasses;
    }

    private boolean isProtobufMessage(TypeElement typeElement, TypeMirror messageType) {
        // Skip abstract classes and interfaces
        if (typeElement.getModifiers().contains(Modifier.ABSTRACT) || typeElement.getKind() == ElementKind.INTERFACE) {
            return false;
        }

        // Check if it's a subtype of Message
        return typeUtils.isSubtype(typeElement.asType(), messageType);
    }

    private String deriveProtobufTypeName(String className) {
        // For classes like user.v1.User, the protobuf type name is usually "user.v1.User"
        // This assumes the Java package structure matches the protobuf package structure
        return className;
    }

    private TypeSpec generateGlobalTypesClass(String className, Set<MessageInfo> messageClasses) {
        // Create the types field
        FieldSpec typesField = FieldSpec.builder(
                        ParameterizedTypeName.get(
                                ClassName.get(Map.class),
                                ClassName.get(String.class),
                                ParameterizedTypeName.get(
                                        ClassName.get(Class.class), WildcardTypeName.subtypeOf(Object.class))),
                        "types",
                        Modifier.PRIVATE,
                        Modifier.STATIC,
                        Modifier.FINAL)
                .initializer("new $T<>()", ConcurrentHashMap.class)
                .build();

        // Create the typeRegistry field
        ClassName typeRegistryClass = ClassName.get("com.google.protobuf.util", "JsonFormat", "TypeRegistry");
        FieldSpec typeRegistryField = FieldSpec.builder(
                        typeRegistryClass, "typeRegistry", Modifier.PRIVATE, Modifier.STATIC, Modifier.VOLATILE)
                .build();

        // Create static block with register calls
        CodeBlock.Builder staticBlockBuilder = CodeBlock.builder();
        for (MessageInfo messageInfo : messageClasses) {
            staticBlockBuilder.addStatement(
                    "register($S, $L.class)", messageInfo.getTypeName(), messageInfo.getClassName());
        }
        staticBlockBuilder.addStatement("// add all protobuf types here");

        // Create private constructor
        MethodSpec constructor =
                MethodSpec.constructorBuilder().addModifiers(Modifier.PRIVATE).build();

        return TypeSpec.classBuilder(className)
                .addModifiers(Modifier.PUBLIC, Modifier.FINAL)
                .addField(typesField)
                .addField(typeRegistryField)
                .addStaticBlock(staticBlockBuilder.build())
                .addMethod(constructor)
                .addMethod(createGetMethod(className, typeRegistryClass))
                .addMethod(createRegisterMethod())
                .addMethod(createBuildTypeRegistryMethod(typeRegistryClass))
                .addMethod(createGetDescriptorMethod())
                .build();
    }

    private MethodSpec createGetMethod(String className, ClassName typeRegistryClass) {
        return MethodSpec.methodBuilder("get")
                .addModifiers(Modifier.PUBLIC, Modifier.STATIC)
                .returns(typeRegistryClass)
                .addCode(CodeBlock.builder()
                        .addStatement("var res = typeRegistry")
                        .beginControlFlow("if (res == null)")
                        .beginControlFlow("synchronized ($L.class)", className)
                        .addStatement("res = typeRegistry")
                        .beginControlFlow("if (res == null)")
                        .addStatement("res = buildTypeRegistry()")
                        .addStatement("typeRegistry = res")
                        .endControlFlow()
                        .endControlFlow()
                        .endControlFlow()
                        .addStatement("return res")
                        .build())
                .build();
    }

    private MethodSpec createRegisterMethod() {
        return MethodSpec.methodBuilder("register")
                .addModifiers(Modifier.PRIVATE, Modifier.STATIC)
                .returns(void.class)
                .addParameter(String.class, "name")
                .addParameter(
                        ParameterizedTypeName.get(ClassName.get(Class.class), WildcardTypeName.subtypeOf(Object.class)),
                        "clz")
                .addStatement("types.put(name, clz)")
                .build();
    }

    private MethodSpec createBuildTypeRegistryMethod(ClassName typeRegistryClass) {
        return MethodSpec.methodBuilder("buildTypeRegistry")
                .addModifiers(Modifier.PRIVATE, Modifier.STATIC)
                .returns(typeRegistryClass)
                .addCode(CodeBlock.builder()
                        .addStatement("var builder = $T.newBuilder()", typeRegistryClass)
                        .beginControlFlow("for (var en : types.entrySet())")
                        .addStatement("var descriptor = getDescriptor(en.getValue())")
                        .beginControlFlow("if (descriptor != null)")
                        .addStatement("builder.add(descriptor)")
                        .endControlFlow()
                        .endControlFlow()
                        .addStatement("return builder.build()")
                        .build())
                .build();
    }

    private MethodSpec createGetDescriptorMethod() {
        ClassName descriptorClass = ClassName.get("com.google.protobuf", "Descriptors", "Descriptor");
        return MethodSpec.methodBuilder("getDescriptor")
                .addModifiers(Modifier.PRIVATE, Modifier.STATIC)
                .returns(descriptorClass)
                .addParameter(
                        ParameterizedTypeName.get(ClassName.get(Class.class), WildcardTypeName.subtypeOf(Object.class)),
                        "clz")
                .addCode(CodeBlock.builder()
                        .addStatement("// if not protobuf message, return null")
                        .beginControlFlow(
                                "if (!$T.class.isAssignableFrom(clz))", ClassName.get("com.google.protobuf", "Message"))
                        .addStatement("return null")
                        .endControlFlow()
                        .beginControlFlow("try")
                        .addStatement("var m = clz.getMethod($S)", "getDescriptor")
                        .addStatement("return ($T) m.invoke(null)", descriptorClass)
                        .nextControlFlow("catch ($T ignored)", Exception.class)
                        .addStatement("return null")
                        .endControlFlow()
                        .build())
                .build();
    }

    /**
     * Holds information about a protobuf Message class.
     */
    private static class MessageInfo {
        private final String typeName;
        private final String className;

        public MessageInfo(String typeName, String className) {
            this.typeName = typeName;
            this.className = className;
        }

        public String getTypeName() {
            return typeName;
        }

        public String getClassName() {
            return className;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            MessageInfo that = (MessageInfo) o;
            return Objects.equals(typeName, that.typeName) && Objects.equals(className, that.className);
        }

        @Override
        public int hashCode() {
            return Objects.hash(typeName, className);
        }

        @Override
        public String toString() {
            return "MessageInfo{typeName='" + typeName + "', className='" + className + "'}";
        }
    }
}
