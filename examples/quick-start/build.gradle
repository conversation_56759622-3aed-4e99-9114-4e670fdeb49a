plugins {
    id 'org.springframework.boot'
}

dependencies {
    implementation(project(":grpc-starters:grpc-boot-starter"))
    implementation("io.grpc:grpc-testing-proto")
    implementation("com.google.protobuf:protobuf-java-util")

    compileOnly(project(":protobuf-typeregistry-processor"))
    annotationProcessor(project(":protobuf-typeregistry-processor"))

    testImplementation(project(":grpc-starters:grpc-starter-test"))
}

apply from: "${rootDir}/gradle/protobuf.gradle"